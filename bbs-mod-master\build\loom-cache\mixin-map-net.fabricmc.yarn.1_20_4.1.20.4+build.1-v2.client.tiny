v1	named	intermediary
FIELD	mchorse/bbs_mod/mixin/client/RenderTickCounterMixin	F	tickDelta	field_1970
FIELD	mchorse/bbs_mod/mixin/client/RenderTickCounterMixin	F	lastFrameDuration	field_1969
FIELD	mchorse/bbs_mod/mixin/client/RenderTickCounterMixin	J	prevTimeMillis	field_1971
FIELD	mchorse/bbs_mod/mixin/client/WindowMixin	I	width	field_5182
FIELD	mchorse/bbs_mod/mixin/client/WindowMixin	I	height	field_5197
FIELD	mchorse/bbs_mod/mixin/client/WindowMixin	I	framebufferWidth	field_5181
FIELD	mchorse/bbs_mod/mixin/client/WindowMixin	I	framebufferHeight	field_5196
FIELD	mchorse/bbs_mod/mixin/client/WindowMixin	I	scaledWidth	field_5180
FIELD	mchorse/bbs_mod/mixin/client/WindowMixin	I	scaledHeight	field_5194
FIELD	mchorse/bbs_mod/mixin/client/WindowMixin	D	scaleFactor	field_5179
FIELD	mchorse/bbs_mod/mixin/client/WorldRendererMixin	Lnet/minecraft/client/gl/Framebuffer;	entityOutlinesFramebuffer	field_4101
METHOD	mchorse/bbs_mod/mixin/client/CameraMixin	(FF)V	setRotation	method_19325
METHOD	mchorse/bbs_mod/mixin/client/CameraMixin	(DDD)V	setPos	method_19327
METHOD	mchorse/bbs_mod/mixin/client/LivingEntityRendererMixin	(Lnet/minecraft/entity/LivingEntity;F)F	getAnimationCounter	method_23185
