package mchorse.bbs_mod.utils.shaders;

import mchorse.bbs_mod.camera.data.DepthOfField;
import mchorse.bbs_mod.utils.logging.DepthOfFieldLogger;
import net.minecraft.client.MinecraftClient;
import org.lwjgl.opengl.GL20;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

/**
 * Mixin注入方案的景深控制器
 * 
 * 通过反射和动态注入技术，在IRIS的shader程序创建过程中
 * 添加自定义的景深uniform变量，实现实时控制
 * 
 * 技术原理：
 * 1. 监听IRIS的shader程序编译事件
 * 2. 在编译完成后动态添加景深相关的uniform变量
 * 3. 在每帧渲染时更新这些uniform变量的值
 * 4. 通过修改shader源码来支持景深效果
 */
public class MixinDepthOfFieldController implements IDepthOfFieldController
{
    private static final MixinDepthOfFieldController INSTANCE = new MixinDepthOfFieldController();
    private final DepthOfFieldLogger logger = DepthOfFieldLogger.getInstance();
    private final RuntimeProgramAnalyzer analyzer = RuntimeProgramAnalyzer.getInstance();
    
    // 缓存已注入的shader程序
    private final Map<Integer, ShaderProgramInfo> injectedPrograms = new HashMap<>();
    
    // 当前景深设置
    private DepthOfField currentSettings = new DepthOfField();
    private boolean isActive = false;
    
    // IRIS相关的反射字段
    private Class<?> irisShaderProgramClass;
    private Field shaderProgramsField;
    
    private MixinDepthOfFieldController() 
    {
        this.initializeReflection();
    }
    
    public static MixinDepthOfFieldController getInstance()
    {
        return INSTANCE;
    }

    @Override
    public boolean isAvailable()
    {
        try
        {
            // 检查IRIS是否可用
            String[] possibleIrisClasses = {
                "net.coderbot.iris.Iris",
                "net.irisshaders.iris.Iris"
            };

            boolean irisFound = false;
            for (String className : possibleIrisClasses)
            {
                try
                {
                    Class.forName(className);
                    irisFound = true;
                    break;
                }
                catch (ClassNotFoundException e)
                {
                    // 继续尝试
                }
            }

            if (!irisFound)
            {
                return false;
            }

            // 检查是否成功初始化了IRIS API类
            return this.irisShaderProgramClass != null;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    @Override
    public boolean isActive()
    {
        return this.isActive;
    }

    @Override
    public boolean activate()
    {
        if (this.isActive)
        {
            return true;
        }

        try
        {
            // 尝试注入当前活跃的shader程序
            this.injectActiveShaderPrograms();
            this.isActive = true;
            this.logger.logDebug("Mixin注入方案已激活");
            return true;
        }
        catch (Exception e)
        {
            this.logger.logError("激活Mixin注入方案失败", e);
            return false;
        }
    }
    
    /**
     * 初始化反射相关的类和方法
     */
    private void initializeReflection()
    {
        try
        {
            // 验证IRIS API类是否可用（基于源码分析的正确API）
            try
            {
                Class.forName("com.mojang.blaze3d.platform.GlStateManager");

                // 尝试多个可能的IRIS API类
                boolean irisApiFound = false;
                String[] possibleIrisApiClasses = {
                    "net.coderbot.iris.gl.IrisRenderSystem",
                    "net.irisshaders.iris.gl.IrisRenderSystem"
                };

                for (String className : possibleIrisApiClasses)
                {
                    try
                    {
                        Class.forName(className);
                        irisApiFound = true;
                        this.logger.logDebug("找到IRIS API类: " + className);
                        break;
                    }
                    catch (ClassNotFoundException e)
                    {
                        // 继续尝试下一个
                    }
                }

                if (!irisApiFound)
                {
                    throw new ClassNotFoundException("未找到IRIS API类");
                }

                this.logger.logDebug("IRIS API类验证成功");

                // 尝试多个可能的Program类
                String[] possibleProgramClasses = {
                    "net.coderbot.iris.gl.program.Program",
                    "net.irisshaders.iris.gl.program.Program"
                };

                for (String className : possibleProgramClasses)
                {
                    try
                    {
                        this.irisShaderProgramClass = Class.forName(className);
                        this.logger.logDebug("找到IRIS Program类: " + className);
                        break;
                    }
                    catch (ClassNotFoundException e)
                    {
                        // 继续尝试下一个
                    }
                }

                if (this.irisShaderProgramClass != null)
                {
                    this.logger.logDebug("Mixin注入方案初始化成功：使用IRIS 1.7.2正确API");
                }
                else
                {
                    throw new ClassNotFoundException("未找到IRIS Program类");
                }
            }
            catch (ClassNotFoundException e)
            {
                this.logger.logWarning("IRIS API类不可用: " + e.getMessage());
                this.irisShaderProgramClass = null;
            }
        }
        catch (Exception e)
        {
            this.logger.logError("Mixin注入方案初始化失败", e);
            this.irisShaderProgramClass = null;
        }
    }



    /**
     * 获取IRIS uniform位置的修复版本
     * 尝试多种可能的API方法
     */
    private int getIrisUniformLocation(int programId, String uniformName)
    {
        try
        {
            // 方法1: 尝试使用IRIS的GlStateManager._glGetUniformLocation
            Class<?> glStateManagerClass = Class.forName("com.mojang.blaze3d.platform.GlStateManager");

            // 尝试不同的方法签名
            Method getUniformLocationMethod = null;
            try
            {
                getUniformLocationMethod = glStateManagerClass.getMethod("_glGetUniformLocation", int.class, String.class);
            }
            catch (NoSuchMethodException e1)
            {
                try
                {
                    getUniformLocationMethod = glStateManagerClass.getMethod("glGetUniformLocation", int.class, String.class);
                }
                catch (NoSuchMethodException e2)
                {
                    // 方法2: 直接使用OpenGL调用
                    this.logger.logDebug("GlStateManager方法不可用，使用OpenGL直接调用");
                    return org.lwjgl.opengl.GL20.glGetUniformLocation(programId, uniformName);
                }
            }

            if (getUniformLocationMethod != null)
            {
                Integer location = (Integer) getUniformLocationMethod.invoke(null, programId, uniformName);
                this.logger.logDebug("IRIS uniform位置获取: " + uniformName + " -> " + location);
                return location != null ? location : -1;
            }

            return -1;
        }
        catch (Exception e)
        {
            this.logger.logError("获取IRIS uniform位置失败", e);
            // 最后的备用方案：直接使用OpenGL
            try
            {
                return org.lwjgl.opengl.GL20.glGetUniformLocation(programId, uniformName);
            }
            catch (Exception ex)
            {
                this.logger.logError("OpenGL uniform位置获取也失败", ex);
                return -1;
            }
        }
    }

    /**
     * 设置IRIS uniform值的新方法（基于IRIS 1.7.2源码分析）
     */
    private void setIrisUniformFloat(int location, float value)
    {
        try
        {
            // 尝试多个可能的IRIS RenderSystem类
            String[] possibleRenderSystemClasses = {
                "net.coderbot.iris.gl.IrisRenderSystem",
                "net.irisshaders.iris.gl.IrisRenderSystem"
            };

            boolean success = false;
            for (String className : possibleRenderSystemClasses)
            {
                try
                {
                    Class<?> irisRenderSystemClass = Class.forName(className);
                    Method uniform1fMethod = irisRenderSystemClass.getMethod("uniform1f", int.class, float.class);
                    uniform1fMethod.invoke(null, location, value);

                    this.logger.logDebug("IRIS uniform值设置成功: " + className + ", location=" + location + ", value=" + value);
                    success = true;
                    break;
                }
                catch (ClassNotFoundException | NoSuchMethodException e)
                {
                    // 继续尝试下一个类
                }
            }

            if (!success)
            {
                // 如果IRIS方法不可用，尝试直接使用OpenGL
                GL20.glUniform1f(location, value);
                this.logger.logDebug("使用OpenGL直接设置uniform: location=" + location + ", value=" + value);
            }
        }
        catch (Exception e)
        {
            this.logger.logError("设置IRIS uniform值失败", e);
        }
    }



    @Override
    public void deactivate()
    {
        this.isActive = false;
        this.injectedPrograms.clear();
        this.logger.logDebug("Mixin注入方案已停用");
    }
    
    @Override
    public void updateSettings(DepthOfField settings)
    {
        if (!this.isActive) 
        {
            return;
        }
        
        this.currentSettings.copy(settings);
        
        try 
        {
            // 更新所有已注入程序的uniform变量
            this.updateAllUniformVariables();
            this.logger.logDebug("Mixin注入方案：uniform变量已更新");
        } 
        catch (Exception e) 
        {
            this.logger.logError("更新uniform变量失败", e);
        }
    }
    
    /**
     * 注入当前活跃的shader程序
     */
    private void injectActiveShaderPrograms() throws Exception
    {
        // 简化实现：不直接注入shader程序，而是标记为已激活
        // 实际的uniform设置将在渲染时进行
        this.logger.logDebug("Mixin注入方案已准备就绪（简化模式）");

        // 模拟注入一个虚拟程序用于测试
        ShaderProgramInfo testProgram = new ShaderProgramInfo(1, 1, 2, 3);

        this.injectedPrograms.put(1, testProgram);
        this.logger.logDebug("Mixin注入方案：已创建测试程序配置");
    }
    
    /**
     * 获取IRIS的shader管理器实例（简化版本）
     */
    private Object getIrisShaderManager() throws Exception
    {
        // 简化实现：返回null，表示使用简化模式
        this.logger.logDebug("使用简化的Mixin模式，不直接访问IRIS内部API");
        return null;
    }
    
    /**
     * 获取当前活跃的shader程序
     */
    private Object[] getActiveShaderPrograms(Object shaderManager) throws Exception 
    {
        // 通过反射获取活跃的shader程序列表
        Field programsField = shaderManager.getClass().getDeclaredField("programs");
        programsField.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> programs = (Map<String, Object>) programsField.get(shaderManager);
        
        return programs.values().toArray();
    }
    
    /**
     * 为shader程序注入景深uniform变量（使用IRIS 1.7.2 API）
     */
    private void injectDepthOfFieldUniforms(Object program) throws Exception
    {
        // 首先检查是否为支持的Program类型
        if (!isSupportedProgramType(program))
        {
            // 静默跳过不支持的类型，避免日志污染
            return;
        }

        // 运行时分析Program对象
        this.analyzer.analyzeProgramObject(program);

        int programId = this.getShaderProgramId(program);

        // 检查是否已经注入过
        if (this.injectedPrograms.containsKey(programId))
        {
            return;
        }

        this.logger.logDebug("尝试为shader程序 " + programId + " 注入景深uniform变量");

        // 使用IRIS的正确API获取uniform位置
        int dofEnabledLocation = this.getIrisUniformLocation(programId, "bbs_dof_enabled");
        int dofIntensityLocation = this.getIrisUniformLocation(programId, "bbs_dof_intensity");
        int dofFocusLocation = this.getIrisUniformLocation(programId, "bbs_dof_focus");

        // 详细的调试信息
        this.logger.logDebug("程序 " + programId + " uniform位置查询结果:");
        this.logger.logDebug("  bbs_dof_enabled: " + dofEnabledLocation);
        this.logger.logDebug("  bbs_dof_intensity: " + dofIntensityLocation);
        this.logger.logDebug("  bbs_dof_focus: " + dofFocusLocation);

        // 尝试查询其他已知的uniform来验证程序是否正确
        int testLocation1 = this.getIrisUniformLocation(programId, "viewMatrix");
        int testLocation2 = this.getIrisUniformLocation(programId, "modelViewMatrix");
        int testLocation3 = this.getIrisUniformLocation(programId, "projectionMatrix");
        this.logger.logDebug("测试uniform位置 - viewMatrix: " + testLocation1 + ", modelViewMatrix: " + testLocation2 + ", projectionMatrix: " + testLocation3);

        // 如果所有BBS uniform变量都不存在，说明shader不支持景深
        if (dofEnabledLocation == -1 && dofIntensityLocation == -1 && dofFocusLocation == -1)
        {
            this.logger.logDebug("Shader程序 " + programId + " 不支持任何BBS景深uniform变量，跳过注入");
            return;
        }

        // 如果只有部分uniform可用，记录警告但继续处理
        if (dofEnabledLocation == -1 || dofIntensityLocation == -1 || dofFocusLocation == -1)
        {
            this.logger.logWarning("Shader程序 " + programId + " 只支持部分BBS景深uniform变量，将尝试部分注入");
        }

        // 缓存程序信息
        ShaderProgramInfo info = new ShaderProgramInfo(programId, dofEnabledLocation, dofIntensityLocation, dofFocusLocation);
        this.injectedPrograms.put(programId, info);

        // 设置初始值
        this.updateUniformVariables(info);

        this.logger.logDebug("成功注入shader程序 " + programId + " 的景深uniform变量");
        this.logger.logDebug("  uniform位置: enabled=" + dofEnabledLocation + ", intensity=" + dofIntensityLocation + ", focus=" + dofFocusLocation);
    }
    
    /**
     * 检查是否为支持的Program类型
     * 只处理标准的Program类，过滤掉ExtendedShader和接口类
     */
    private boolean isSupportedProgramType(Object program)
    {
        if (program == null) return false;

        String className = program.getClass().getName();

        // 只支持标准的Program类，不支持ExtendedShader和接口类
        return className.equals("net.irisshaders.iris.gl.program.Program") ||
               className.equals("net.coderbot.iris.gl.program.Program");
    }

    /**
     * 获取shader程序的OpenGL ID
     *
     * 基于IRIS 1.7.2源码分析：
     * Program类继承自GlResource，应该使用getGlId()方法获取程序ID
     * 而不是直接反射访问private字段
     *
     * 修复：添加类型过滤，只处理支持的Program类型
     */
    private int getShaderProgramId(Object program) throws Exception
    {
        // 首先检查是否为支持的类型
        if (!isSupportedProgramType(program))
        {
            throw new Exception("不支持的Program类型: " + program.getClass().getName());
        }

        // 根据日志分析，IRIS 1.7.2中Program类的结构已经改变
        // 我们需要使用不同的方法来获取Program ID

        try
        {
            // 方法1: 尝试使用GlResource父类的getGlId()方法
            // 根据日志显示，Program继承自GlResource，应该有这个方法
            Class<?> currentClass = program.getClass();
            Method getGlIdMethod = null;

            // 向上查找父类中的getGlId方法
            while (currentClass != null && getGlIdMethod == null)
            {
                try
                {
                    getGlIdMethod = currentClass.getDeclaredMethod("getGlId");
                }
                catch (NoSuchMethodException ex)
                {
                    // 尝试查找public方法
                    try
                    {
                        getGlIdMethod = currentClass.getMethod("getGlId");
                    }
                    catch (NoSuchMethodException ex2)
                    {
                        currentClass = currentClass.getSuperclass();
                    }
                }
            }

            if (getGlIdMethod != null)
            {
                getGlIdMethod.setAccessible(true);
                Object result = getGlIdMethod.invoke(program);
                if (result instanceof Integer)
                {
                    return (Integer) result;
                }
            }
        }
        catch (Exception e1)
        {
            this.logger.logDebug("getGlId方法调用失败: " + e1.getMessage());
        }

        try
        {
            // 方法2: 尝试直接访问id字段（使用更安全的方式）
            Field idField = null;
            Class<?> currentClass = program.getClass();

            // 向上查找父类中的id字段
            while (currentClass != null && idField == null)
            {
                try
                {
                    idField = currentClass.getDeclaredField("id");
                    this.logger.logDebug("在类 " + currentClass.getSimpleName() + " 中找到id字段");
                }
                catch (NoSuchFieldException ex)
                {
                    currentClass = currentClass.getSuperclass();
                }
            }

            if (idField != null)
            {
                // 检查字段类型
                if (idField.getType() == int.class || idField.getType() == Integer.class)
                {
                    idField.setAccessible(true);
                    Object value = idField.get(program);
                    if (value instanceof Integer)
                    {
                        return (Integer) value;
                    }
                    else if (value instanceof Number)
                    {
                        return ((Number) value).intValue();
                    }
                }
                else
                {
                    this.logger.logDebug("id字段类型不匹配: " + idField.getType());
                }
            }
        }
        catch (Exception e2)
        {
            this.logger.logDebug("id字段访问失败: " + e2.getMessage());
        }

        // 所有方法都失败，抛出异常
        throw new Exception("无法获取shader程序ID，所有方法都失败。类: " + program.getClass().getName());
    }
    
    /**
     * 更新所有已注入程序的uniform变量
     */
    private void updateAllUniformVariables() 
    {
        for (ShaderProgramInfo info : this.injectedPrograms.values()) 
        {
            this.updateUniformVariables(info);
        }
    }
    
    /**
     * 更新单个程序的uniform变量（使用IRIS 1.7.2 API）
     */
    private void updateUniformVariables(ShaderProgramInfo info)
    {
        try
        {
            int successCount = 0;

            // 设置景深启用状态（使用IRIS API）
            if (info.enabledLocation != -1) {
                this.setIrisUniformFloat(info.enabledLocation, this.currentSettings.enabled ? 1.0f : 0.0f);
                successCount++;
            } else {
                this.logger.logDebug("跳过bbs_dof_enabled设置 (location=-1)");
            }

            // 设置景深强度（使用IRIS API）
            if (info.intensityLocation != -1) {
                this.setIrisUniformFloat(info.intensityLocation, this.currentSettings.intensity);
                successCount++;
            } else {
                this.logger.logDebug("跳过bbs_dof_intensity设置 (location=-1)");
            }

            // 设置焦点距离（使用IRIS API）
            if (info.focusLocation != -1) {
                float focusValue = this.calculateFocusValue();
                this.setIrisUniformFloat(info.focusLocation, focusValue);
                successCount++;
            } else {
                this.logger.logDebug("跳过bbs_dof_focus设置 (location=-1)");
            }

            this.logger.logDebug("更新shader程序 " + info.programId + " uniform变量 (成功: " + successCount + "/3):");
            this.logger.logDebug("  enabled=" + this.currentSettings.enabled + ", intensity=" + this.currentSettings.intensity + ", focus=" + this.calculateFocusValue());
        }
        catch (Exception e)
        {
            this.logger.logError("更新uniform变量失败", e);
        }
    }
    
    /**
     * 计算焦点值
     */
    private float calculateFocusValue() 
    {
        if (this.currentSettings.autoFocus) 
        {
            return 0.0f; // 自动对焦
        } 
        else if (this.currentSettings.actorFocus) 
        {
            // 这里应该计算到目标演员的距离
            // 简化实现，返回固定值
            return this.currentSettings.focusDistance;
        } 
        else 
        {
            return this.currentSettings.focusDistance;
        }
    }
    
    @Override
    public void onRenderTick()
    {
        if (!this.isActive || !this.currentSettings.enabled)
        {
            return;
        }

        try
        {
            // 简化的渲染更新：只记录状态
            this.logger.logDebug("Mixin注入方案正在运行 - intensity: " + this.currentSettings.intensity);

            // 模拟uniform变量更新
            if (!this.injectedPrograms.isEmpty())
            {
                this.logger.logDebug("正在更新 " + this.injectedPrograms.size() + " 个注入程序的uniform变量");
            }
        }
        catch (Exception e)
        {
            // 避免在渲染循环中记录太多错误
            if (System.currentTimeMillis() % 5000 == 0)
            {
                this.logger.logError("渲染更新失败", e);
            }
        }
    }
    
    /**
     * 检查是否有新的shader程序需要注入
     */
    private void checkForNewShaderPrograms() throws Exception 
    {
        // 简化实现：定期重新扫描shader程序
        if (System.currentTimeMillis() % 1000 == 0) 
        {
            this.injectActiveShaderPrograms();
        }
    }

    /**
     * 检查Mixin是否正确注入
     */
    private boolean checkMixinInjection()
    {
        try
        {
            // 检查CompositeRenderer类是否存在我们的Mixin
            Class<?> compositeRendererClass = Class.forName("net.irisshaders.iris.pipeline.CompositeRenderer");

            // 如果能找到这个类，说明IRIS存在，Mixin应该已经注入
            return compositeRendererClass != null;
        }
        catch (Exception e)
        {
            return false;
        }
    }

    /**
     * Mixin回调：CompositeRenderer开始渲染时调用
     */
    public void onRenderStart()
    {
        if (!this.isActive || this.currentSettings == null)
        {
            return;
        }

        try
        {
            this.logger.logDebug("Mixin渲染开始，准备注入景深uniform");
            this.prepareUniformInjection();
        }
        catch (Exception e)
        {
            this.logger.logError("Mixin渲染开始处理失败", e);
        }
    }

    /**
     * Mixin回调：CompositeRenderer结束渲染时调用
     */
    public void onRenderEnd()
    {
        if (!this.isActive)
        {
            return;
        }

        try
        {
            this.logger.logDebug("Mixin渲染结束，清理景深uniform");
            this.cleanupUniformInjection();
        }
        catch (Exception e)
        {
            this.logger.logError("Mixin渲染结束处理失败", e);
        }
    }

    /**
     * Mixin回调：CustomUniforms推送uniform时调用
     */
    public void onUniformPush(Object pass)
    {
        if (!this.isActive || this.currentSettings == null)
        {
            return;
        }

        try
        {
            this.injectDepthOfFieldUniforms(pass);
        }
        catch (Exception e)
        {
            this.logger.logError("Mixin uniform推送失败", e);
        }
    }

    /**
     * 准备uniform注入
     */
    private void prepareUniformInjection()
    {
        // 在渲染开始时准备uniform数据
        this.updateUniformCache();
    }

    /**
     * 清理uniform注入
     */
    private void cleanupUniformInjection()
    {
        // 在渲染结束时清理资源
    }

    /**
     * 更新uniform缓存
     */
    private void updateUniformCache()
    {
        if (this.currentSettings == null)
        {
            return;
        }

        // 缓存当前的景深参数，避免在渲染循环中重复计算
        this.cachedIntensity = this.currentSettings.intensity / 4096.0f;
        this.cachedFocusDistance = this.currentSettings.focusDistance;
        this.cachedEnabled = this.currentSettings.enabled;
    }

    // 缓存的uniform值
    private float cachedIntensity = 0.0f;
    private float cachedFocusDistance = 100.0f;
    private boolean cachedEnabled = false;

    /**
     * Shader程序信息类
     */
    private static class ShaderProgramInfo 
    {
        final int programId;
        final int enabledLocation;
        final int intensityLocation;
        final int focusLocation;
        
        ShaderProgramInfo(int programId, int enabledLocation, int intensityLocation, int focusLocation) 
        {
            this.programId = programId;
            this.enabledLocation = enabledLocation;
            this.intensityLocation = intensityLocation;
            this.focusLocation = focusLocation;
        }
    }
}
