{"mappings": {"mchorse/bbs_mod/mixin/client/CameraMixin": {"update": "Lnet/minecraft/class_4184;method_19321(Lnet/minecraft/class_1922;Lnet/minecraft/class_1297;ZZF)V"}, "mchorse/bbs_mod/mixin/client/ClientWorldPropertiesMixin": {"getTimeOfDay": "Lnet/minecraft/class_638$class_5271;method_217()J"}, "mchorse/bbs_mod/mixin/client/EntityRendererDispatcherInvoker": {"renderShadow": "method_23166(Lnet/minecraft/class_4587;Lnet/minecraft/class_4597;Lnet/minecraft/class_1297;FFLnet/minecraft/class_4538;F)V"}, "mchorse/bbs_mod/mixin/client/EntityRendererMixin": {"renderLabelIfPresent": "Lnet/minecraft/class_897;method_3926(Lnet/minecraft/class_1297;Lnet/minecraft/class_2561;Lnet/minecraft/class_4587;Lnet/minecraft/class_4597;I)V"}, "mchorse/bbs_mod/mixin/client/GameRendererMixin": {"bobView": "Lnet/minecraft/class_757;method_3186(Lnet/minecraft/class_4587;F)V", "getFov": "Lnet/minecraft/class_757;method_3196(Lnet/minecraft/class_4184;FZ)D", "renderHand": "Lnet/minecraft/class_757;method_3172(Lnet/minecraft/class_4587;Lnet/minecraft/class_4184;F)V", "renderWorld": "Lnet/minecraft/class_757;method_3188(FJLnet/minecraft/class_4587;)V", "tiltViewWhenHurt": "Lnet/minecraft/class_757;method_3198(Lnet/minecraft/class_4587;F)V"}, "mchorse/bbs_mod/mixin/client/InGameHudMixin": {"render": "Lnet/minecraft/class_329;method_1753(Lnet/minecraft/class_332;F)V"}, "mchorse/bbs_mod/mixin/client/IntegratedServerMixin": {"Lnet/minecraft/server/MinecraftServer;tick(Ljava/util/function/BooleanSupplier;)V": "Lnet/minecraft/server/MinecraftServer;method_3748(Ljava/util/function/BooleanSupplier;)V", "tick": "Lnet/minecraft/class_1132;method_3748(Ljava/util/function/BooleanSupplier;)V"}, "mchorse/bbs_mod/mixin/client/KeyboardInputMixin": {"tick": "Lnet/minecraft/class_743;method_3129(ZF)V"}, "mchorse/bbs_mod/mixin/client/KeyboardMixin": {"onKey": "Lnet/minecraft/class_309;method_1466(JIIII)V"}, "mchorse/bbs_mod/mixin/client/LanguageManagerMixin": {"reload": "Lnet/minecraft/class_1076;method_14491(Lnet/minecraft/class_3300;)V"}, "mchorse/bbs_mod/mixin/client/LivingEntityRendererMixin": {"render": "Lnet/minecraft/class_922;method_4054(Lnet/minecraft/class_1309;FFLnet/minecraft/class_4587;Lnet/minecraft/class_4597;I)V"}, "mchorse/bbs_mod/mixin/client/LivingEntityUpdateMixin": {"baseTick": "Lnet/minecraft/class_1309;method_5670()V"}, "mchorse/bbs_mod/mixin/client/MouseMixin": {"onMouseScroll": "Lnet/minecraft/class_312;method_1598(JDD)V"}, "mchorse/bbs_mod/mixin/client/PlayerEntityRendererMixin": {"getPositionOffset": "Lnet/minecraft/class_1007;method_23206(Lnet/minecraft/class_742;F)Lnet/minecraft/class_243;", "render": "Lnet/minecraft/class_1007;method_4215(Lnet/minecraft/class_742;FFLnet/minecraft/class_4587;Lnet/minecraft/class_4597;I)V"}, "mchorse/bbs_mod/mixin/client/RenderLayerMixin": {"Lnet/minecraft/client/render/RenderLayer;startDrawing()V": "Lnet/minecraft/class_1921;method_23516()V", "draw": "Lnet/minecraft/class_1921;method_23012(Lnet/minecraft/class_287;Lnet/minecraft/class_8251;)V"}, "mchorse/bbs_mod/mixin/client/RenderTickCounterMixin": {"beginRenderTick": "Lnet/minecraft/class_317;method_1658(J)I"}, "mchorse/bbs_mod/mixin/client/ResourceReloadLoggerMixin": {"finish": "Lnet/minecraft/class_6360;method_36562()V"}, "mchorse/bbs_mod/mixin/client/WindowMixin": {"getFramebufferHeight": "Lnet/minecraft/class_1041;method_4506()I", "getFramebufferWidth": "Lnet/minecraft/class_1041;method_4489()I", "getHeight": "Lnet/minecraft/class_1041;method_4507()I", "getScaledHeight": "Lnet/minecraft/class_1041;method_4502()I", "getScaledWidth": "Lnet/minecraft/class_1041;method_4486()I", "getWidth": "Lnet/minecraft/class_1041;method_4480()I"}, "mchorse/bbs_mod/mixin/client/WorldRendererMixin": {"loadEntityOutlinePostProcessor": "Lnet/minecraft/class_761;method_3296()V", "onResized": "Lnet/minecraft/class_761;method_3242(II)V", "renderLayer": "Lnet/minecraft/class_761;method_3251(Lnet/minecraft/class_1921;Lnet/minecraft/class_4587;DDDLorg/joml/Matrix4f;)V"}}, "data": {"named:intermediary": {"mchorse/bbs_mod/mixin/client/CameraMixin": {"update": "Lnet/minecraft/class_4184;method_19321(Lnet/minecraft/class_1922;Lnet/minecraft/class_1297;ZZF)V"}, "mchorse/bbs_mod/mixin/client/ClientWorldPropertiesMixin": {"getTimeOfDay": "Lnet/minecraft/class_638$class_5271;method_217()J"}, "mchorse/bbs_mod/mixin/client/EntityRendererDispatcherInvoker": {"renderShadow": "method_23166(Lnet/minecraft/class_4587;Lnet/minecraft/class_4597;Lnet/minecraft/class_1297;FFLnet/minecraft/class_4538;F)V"}, "mchorse/bbs_mod/mixin/client/EntityRendererMixin": {"renderLabelIfPresent": "Lnet/minecraft/class_897;method_3926(Lnet/minecraft/class_1297;Lnet/minecraft/class_2561;Lnet/minecraft/class_4587;Lnet/minecraft/class_4597;I)V"}, "mchorse/bbs_mod/mixin/client/GameRendererMixin": {"bobView": "Lnet/minecraft/class_757;method_3186(Lnet/minecraft/class_4587;F)V", "getFov": "Lnet/minecraft/class_757;method_3196(Lnet/minecraft/class_4184;FZ)D", "renderHand": "Lnet/minecraft/class_757;method_3172(Lnet/minecraft/class_4587;Lnet/minecraft/class_4184;F)V", "renderWorld": "Lnet/minecraft/class_757;method_3188(FJLnet/minecraft/class_4587;)V", "tiltViewWhenHurt": "Lnet/minecraft/class_757;method_3198(Lnet/minecraft/class_4587;F)V"}, "mchorse/bbs_mod/mixin/client/InGameHudMixin": {"render": "Lnet/minecraft/class_329;method_1753(Lnet/minecraft/class_332;F)V"}, "mchorse/bbs_mod/mixin/client/IntegratedServerMixin": {"Lnet/minecraft/server/MinecraftServer;tick(Ljava/util/function/BooleanSupplier;)V": "Lnet/minecraft/server/MinecraftServer;method_3748(Ljava/util/function/BooleanSupplier;)V", "tick": "Lnet/minecraft/class_1132;method_3748(Ljava/util/function/BooleanSupplier;)V"}, "mchorse/bbs_mod/mixin/client/KeyboardInputMixin": {"tick": "Lnet/minecraft/class_743;method_3129(ZF)V"}, "mchorse/bbs_mod/mixin/client/KeyboardMixin": {"onKey": "Lnet/minecraft/class_309;method_1466(JIIII)V"}, "mchorse/bbs_mod/mixin/client/LanguageManagerMixin": {"reload": "Lnet/minecraft/class_1076;method_14491(Lnet/minecraft/class_3300;)V"}, "mchorse/bbs_mod/mixin/client/LivingEntityRendererMixin": {"render": "Lnet/minecraft/class_922;method_4054(Lnet/minecraft/class_1309;FFLnet/minecraft/class_4587;Lnet/minecraft/class_4597;I)V"}, "mchorse/bbs_mod/mixin/client/LivingEntityUpdateMixin": {"baseTick": "Lnet/minecraft/class_1309;method_5670()V"}, "mchorse/bbs_mod/mixin/client/MouseMixin": {"onMouseScroll": "Lnet/minecraft/class_312;method_1598(JDD)V"}, "mchorse/bbs_mod/mixin/client/PlayerEntityRendererMixin": {"getPositionOffset": "Lnet/minecraft/class_1007;method_23206(Lnet/minecraft/class_742;F)Lnet/minecraft/class_243;", "render": "Lnet/minecraft/class_1007;method_4215(Lnet/minecraft/class_742;FFLnet/minecraft/class_4587;Lnet/minecraft/class_4597;I)V"}, "mchorse/bbs_mod/mixin/client/RenderLayerMixin": {"Lnet/minecraft/client/render/RenderLayer;startDrawing()V": "Lnet/minecraft/class_1921;method_23516()V", "draw": "Lnet/minecraft/class_1921;method_23012(Lnet/minecraft/class_287;Lnet/minecraft/class_8251;)V"}, "mchorse/bbs_mod/mixin/client/RenderTickCounterMixin": {"beginRenderTick": "Lnet/minecraft/class_317;method_1658(J)I"}, "mchorse/bbs_mod/mixin/client/ResourceReloadLoggerMixin": {"finish": "Lnet/minecraft/class_6360;method_36562()V"}, "mchorse/bbs_mod/mixin/client/WindowMixin": {"getFramebufferHeight": "Lnet/minecraft/class_1041;method_4506()I", "getFramebufferWidth": "Lnet/minecraft/class_1041;method_4489()I", "getHeight": "Lnet/minecraft/class_1041;method_4507()I", "getScaledHeight": "Lnet/minecraft/class_1041;method_4502()I", "getScaledWidth": "Lnet/minecraft/class_1041;method_4486()I", "getWidth": "Lnet/minecraft/class_1041;method_4480()I"}, "mchorse/bbs_mod/mixin/client/WorldRendererMixin": {"loadEntityOutlinePostProcessor": "Lnet/minecraft/class_761;method_3296()V", "onResized": "Lnet/minecraft/class_761;method_3242(II)V", "renderLayer": "Lnet/minecraft/class_761;method_3251(Lnet/minecraft/class_1921;Lnet/minecraft/class_4587;DDDLorg/joml/Matrix4f;)V"}}}}