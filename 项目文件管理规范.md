# 项目文件管理规范

## 📋 Memory管理规范

### Memory清理原则
1. **定期清理过时信息**：删除已解决问题的临时记录
2. **保留核心信息**：保留项目关键技术要点和最终状态
3. **避免重复记录**：合并相似的技术要点
4. **记录清理活动**：每次清理都要记录到memory中

### 当前Memory状态（已清理）
- ✅ 删除了所有过时的测试版本记录
- ✅ 删除了已解决的问题诊断记录
- ✅ 保留了最终实现状态和关键技术要点
- ✅ 记录了文件管理规范要求

## 📦 Finally文件夹归档规范

### 归档触发条件
1. **项目重要里程碑完成**
2. **用户明确要求归档**
3. **重大功能实现完成**
4. **问题解决和修复完成**

### 归档内容标准
```
finally/BBS-MOD-Complete-YYYY-MM-DD_HH-mm-ss.zip
├── shaders/
│   ├── ComplementaryReimagined-Original-Clean.zip
│   └── ComplementaryReimagined-BBS-Complete.zip
├── mods/
│   ├── iris-1.7.2-snapshot+mc1.20.4-bbs-debug.jar
│   └── bbs-builds/
│       └── bbs-1.2.7-nts-1.20.4.jar
├── docs/
│   ├── 重要技术文档.md
│   └── 问题解决报告.md
└── README.md
```

### 归档文件命名规范
- 格式：`BBS-MOD-Complete-YYYY-MM-DD_HH-mm-ss.zip`
- 包含时间戳确保唯一性
- 使用统一的前缀标识项目

## 🔄 工作流程规范

### 每次任务完成后
1. **清理临时文件**：删除构建过程中的临时文件
2. **整理项目目录**：保持目录结构清晰
3. **更新Memory**：记录关键技术要点和最终状态
4. **创建归档包**：将重要文件打包到finally文件夹

### Memory更新规范
```
记录格式：
- 项目状态：完成的功能和特性
- 技术要点：关键实现方法和注意事项
- 文件清单：最终生成的重要文件
- 管理要求：文件管理和归档要求
```

## 📁 目录结构维护

### 保留的核心文件
```
d:\bbsmod\
├── ComplementaryReimagined-Original-Clean.zip     # 原版光影包
├── ComplementaryReimagined-BBS-Complete.zip       # BBS完整集成版
├── iris-1.7.2-snapshot+mc1.20.4-bbs-debug.jar   # 修改后的IRIS
├── bbs-mod-master/                                # BBS MOD源码
├── iris-src/                                     # IRIS源码
├── finally/                                      # 归档文件夹
└── 重要文档.md                                    # 技术文档
```

### 定期清理的文件
- 临时构建文件（.class, .java临时文件）
- 测试版本文件
- 重复的压缩包
- 过时的日志文件

## 🎯 执行检查清单

### 任务完成检查
- [ ] 功能是否完全实现
- [ ] 所有测试是否通过
- [ ] 临时文件是否清理
- [ ] Memory是否更新
- [ ] 是否需要创建归档

### 归档检查
- [ ] 重要文件是否包含完整
- [ ] README是否准确描述内容
- [ ] 文件路径是否正确
- [ ] 归档文件是否可以独立使用

## 📝 当前项目状态

### 最新归档
- **文件名**：`BBS-MOD-Complete-2025-08-04_12-25-07.zip`
- **内容**：完整的BBS景深功能实现
- **状态**：可直接部署使用

### Memory状态
- **已清理**：过时的技术记录和问题诊断
- **保留**：核心实现要点和文件管理规范
- **更新**：项目完成状态和归档要求

## 🔧 自动化工具

项目包含自动化打包工具，可以：
- 自动收集重要文件
- 生成标准化的归档结构
- 创建详细的README文档
- 使用时间戳确保文件唯一性

这确保了项目文件的规范管理和长期维护。
