package mchorse.bbs_mod.utils.shaders;

import mchorse.bbs_mod.utils.logging.DepthOfFieldLogger;
import org.lwjgl.opengl.GL11;
import org.lwjgl.opengl.GL20;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Set;

/**
 * 运行时Program分析器
 * 
 * 在IRIS实际渲染过程中捕获Program对象，分析其真实结构和ID变化
 * 用于验证修复后的getShaderProgramId方法是否正确工作
 */
public class RuntimeProgramAnalyzer 
{
    private static final RuntimeProgramAnalyzer INSTANCE = new RuntimeProgramAnalyzer();
    private final DepthOfFieldLogger logger = DepthOfFieldLogger.getInstance();
    
    private final Set<String> analyzedClasses = new HashSet<>();
    private final Set<Integer> capturedProgramIds = new HashSet<>();
    private boolean isAnalysisEnabled = true;
    private int analysisCount = 0;
    private int skipCount = 0; // 跳过重复分析的次数
    private static final int MAX_ANALYSIS_COUNT = 10; // 限制分析次数避免刷屏
    
    private RuntimeProgramAnalyzer() {}
    
    public static RuntimeProgramAnalyzer getInstance() 
    {
        return INSTANCE;
    }
    
    /**
     * 分析运行时的Program对象
     */
    public void analyzeProgramObject(Object programObject)
    {
        if (!this.isAnalysisEnabled || this.analysisCount >= MAX_ANALYSIS_COUNT)
        {
            // 静默跳过，避免日志污染
            return;
        }

        if (programObject == null)
        {
            return;
        }

        String className = programObject.getClass().getName();

        // 只分析支持的Program类型，过滤掉ExtendedShader和接口类
        if (!className.equals("net.irisshaders.iris.gl.program.Program") &&
            !className.equals("net.coderbot.iris.gl.program.Program"))
        {
            return;
        }

        // 避免重复分析相同的类
        if (this.analyzedClasses.contains(className))
        {
            // 记录跳过次数但不输出详细日志
            this.skipCount++;
            if (this.skipCount % 20 == 0) {
                this.logger.logDebug("跳过重复分析 (" + this.skipCount + "次): " + className);
            }
            return;
        }
        
        this.analyzedClasses.add(className);
        this.analysisCount++;
        
        this.logger.logDebug("=== 运行时Program对象分析 #" + this.analysisCount + " ===");
        this.logger.logDebug("对象类名: " + className);
        
        try 
        {
            // 测试我们修复后的getShaderProgramId方法
            this.testGetShaderProgramId(programObject);
            
            // 分析对象的实际结构
            this.analyzeObjectStructure(programObject);
            
            // 测试ID的稳定性
            this.testIdStability(programObject);
            
        }
        catch (Exception e) 
        {
            this.logger.logError("运行时Program分析失败", e);
        }
        
        this.logger.logDebug("=== 分析完成 ===");
    }
    
    /**
     * 测试修复后的getShaderProgramId方法
     */
    private void testGetShaderProgramId(Object programObject) 
    {
        this.logger.logDebug("--- 测试getShaderProgramId方法 ---");

        try
        {
            // 使用修复后的方法获取ID
            int programId = this.getShaderProgramIdFixed(programObject);
            this.logger.logDebug("✅ 成功获取程序ID: " + programId);
            this.capturedProgramIds.add(programId);

            // 验证ID的有效性
            if (programId > 0)
            {
                this.logger.logDebug("✅ 程序ID有效 (>0)");
            }
            else
            {
                this.logger.logWarning("⚠️ 程序ID可能无效: " + programId);
            }
        }
        catch (Exception e) 
        {
            this.logger.logError("❌ getShaderProgramId方法测试失败", e);
        }
    }
    
    /**
     * 修复后的getShaderProgramId方法（复制自MixinDepthOfFieldController）
     */
    private int getShaderProgramIdFixed(Object program) throws Exception
    {
        // 根据日志分析，IRIS 1.7.2中Program类的结构已经改变
        // 我们需要使用不同的方法来获取Program ID

        try
        {
            // 方法1: 尝试使用GlResource父类的getGlId()方法
            // 根据日志显示，Program继承自GlResource，应该有这个方法
            Class<?> currentClass = program.getClass();
            Method getGlIdMethod = null;

            // 向上查找父类中的getGlId方法
            while (currentClass != null && getGlIdMethod == null)
            {
                try
                {
                    getGlIdMethod = currentClass.getDeclaredMethod("getGlId");
                }
                catch (NoSuchMethodException ex)
                {
                    // 尝试查找public方法
                    try
                    {
                        getGlIdMethod = currentClass.getMethod("getGlId");
                    }
                    catch (NoSuchMethodException ex2)
                    {
                        currentClass = currentClass.getSuperclass();
                    }
                }
            }

            if (getGlIdMethod != null)
            {
                getGlIdMethod.setAccessible(true);
                Object result = getGlIdMethod.invoke(program);
                if (result instanceof Integer)
                {
                    return (Integer) result;
                }
            }
        }
        catch (Exception e1)
        {
            this.logger.logDebug("getGlId方法调用失败: " + e1.getMessage());
        }

        try
        {
            // 方法2: 尝试直接访问id字段（使用更安全的方式）
            Field idField = null;
            Class<?> currentClass = program.getClass();

            // 向上查找父类中的id字段
            while (currentClass != null && idField == null)
            {
                try
                {
                    idField = currentClass.getDeclaredField("id");
                    this.logger.logDebug("在类 " + currentClass.getSimpleName() + " 中找到id字段");
                }
                catch (NoSuchFieldException ex)
                {
                    currentClass = currentClass.getSuperclass();
                }
            }

            if (idField != null)
            {
                // 检查字段类型
                if (idField.getType() == int.class || idField.getType() == Integer.class)
                {
                    idField.setAccessible(true);
                    Object value = idField.get(program);
                    if (value instanceof Integer)
                    {
                        return (Integer) value;
                    }
                    else if (value instanceof Number)
                    {
                        return ((Number) value).intValue();
                    }
                }
                else
                {
                    this.logger.logDebug("id字段类型不匹配: " + idField.getType());
                }
            }
        }
        catch (Exception e2)
        {
            this.logger.logDebug("id字段访问失败: " + e2.getMessage());
        }

        // 方法3: 使用OpenGL状态查询（最后的备用方案）
        try
        {
            // 如果以上方法都失败，尝试使用当前绑定的程序ID
            // 这不是最理想的方案，但可以作为备用
            int currentProgram = GL11.glGetInteger(GL20.GL_CURRENT_PROGRAM);
            if (currentProgram > 0)
            {
                this.logger.logDebug("使用当前绑定的OpenGL程序ID: " + currentProgram);
                return currentProgram;
            }
        }
        catch (Exception e3)
        {
            this.logger.logDebug("OpenGL状态查询失败: " + e3.getMessage());
        }

        // 所有方法都失败，抛出异常
        throw new Exception("无法获取shader程序ID，所有方法都失败。类: " + program.getClass().getName());
    }
    
    /**
     * 分析对象的实际结构
     */
    private void analyzeObjectStructure(Object programObject) 
    {
        this.logger.logDebug("--- 对象结构分析 ---");

        Class<?> objectClass = programObject.getClass();

        // 分析继承层次
        this.logger.logDebug("继承层次:");
        Class<?> currentClass = objectClass;
        int level = 0;
        while (currentClass != null && level < 5)
        {
            String indent = "  ".repeat(level);
            this.logger.logDebug(indent + "- " + currentClass.getName());
            currentClass = currentClass.getSuperclass();
            level++;
        }

        // 分析可用的方法
        this.logger.logDebug("ID相关方法:");
        Method[] methods = objectClass.getMethods();
        for (Method method : methods)
        {
            String methodName = method.getName();
            if (methodName.toLowerCase().contains("id") ||
                methodName.equals("getGlId") ||
                methodName.equals("getId"))
            {
                this.logger.logDebug("  - " + method.getReturnType().getSimpleName() + " " + methodName + "()");
            }
        }

        // 分析字段（包括父类）
        this.logger.logDebug("ID相关字段:");
        currentClass = objectClass;
        while (currentClass != null)
        {
            Field[] fields = currentClass.getDeclaredFields();
            for (Field field : fields)
            {
                String fieldName = field.getName();
                if (fieldName.toLowerCase().contains("id"))
                {
                    this.logger.logDebug("  - " + currentClass.getSimpleName() + "." +
                        field.getType().getSimpleName() + " " + fieldName +
                        " (" + java.lang.reflect.Modifier.toString(field.getModifiers()) + ")");
                }
            }
            currentClass = currentClass.getSuperclass();
        }
    }
    
    /**
     * 测试ID的稳定性
     */
    private void testIdStability(Object programObject) 
    {
        this.logger.logDebug("--- ID稳定性测试 ---");

        try
        {
            // 多次获取ID，检查是否一致
            int id1 = this.getShaderProgramIdFixed(programObject);
            Thread.sleep(1); // 短暂延迟
            int id2 = this.getShaderProgramIdFixed(programObject);

            if (id1 == id2)
            {
                this.logger.logDebug("✅ ID稳定: " + id1 + " == " + id2);
            }
            else
            {
                this.logger.logWarning("⚠️ ID不稳定: " + id1 + " != " + id2);
            }
        }
        catch (Exception e) 
        {
            this.logger.logError("ID稳定性测试失败", e);
        }
    }
    
    /**
     * 获取分析统计信息
     */
    public String getAnalysisStats() 
    {
        return String.format("已分析类: %d, 捕获程序ID: %s, 分析次数: %d/%d", 
            this.analyzedClasses.size(), 
            this.capturedProgramIds.toString(),
            this.analysisCount,
            MAX_ANALYSIS_COUNT);
    }
    
    /**
     * 启用/禁用分析
     */
    public void setAnalysisEnabled(boolean enabled) 
    {
        this.isAnalysisEnabled = enabled;
        if (enabled)
        {
            this.logger.logDebug("运行时Program分析已启用");
        }
        else
        {
            this.logger.logDebug("运行时Program分析已禁用");
        }
    }
    
    /**
     * 重置分析状态
     */
    public void resetAnalysis()
    {
        this.analyzedClasses.clear();
        this.capturedProgramIds.clear();
        this.analysisCount = 0;
        this.skipCount = 0;
        this.logger.logDebug("运行时Program分析状态已重置");
    }
    
    /**
     * 获取捕获的程序ID列表
     */
    public Set<Integer> getCapturedProgramIds() 
    {
        return new HashSet<>(this.capturedProgramIds);
    }
}
